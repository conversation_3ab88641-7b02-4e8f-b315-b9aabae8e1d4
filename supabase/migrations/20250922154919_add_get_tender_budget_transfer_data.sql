drop function if exists "public"."create_budget_transfer" (
	project_id_param uuid,
	line_item_id_param uuid,
	from_wbs_item_id uuid,
	to_wbs_item_id uuid,
	transfer_amount numeric,
	reason text
);

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.create_budget_transfer (
	project_id_param uuid,
	from_wbs_item_id uuid,
	to_wbs_item_id uuid,
	transfer_amount numeric,
	reason text,
	line_item_id_param uuid DEFAULT NULL::uuid
) RETURNS TABLE (
	budget_transfer_id uuid,
	is_valid boolean,
	error_message text
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    validation_result record;
    new_transfer_id uuid;
    line_item_project_id uuid;
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_param, 'editor') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Ensure the optional tender line item belongs to the same project
    IF line_item_id_param IS NOT NULL THEN
        SELECT t.project_id
        INTO line_item_project_id
        FROM public.tender_line_item tli
        JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id
        JOIN public.tender t ON tr.tender_id = t.tender_id
        WHERE tli.tender_line_item_id = line_item_id_param;

        IF line_item_project_id IS NULL THEN
            RETURN QUERY
            SELECT
                NULL::uuid,
                false,
                'Tender line item not found';
            RETURN;
        ELSIF line_item_project_id <> project_id_param THEN
            RETURN QUERY
            SELECT
                NULL::uuid,
                false,
                'Tender line item does not belong to project';
            RETURN;
        END IF;
    END IF;

    -- Validate the transfer first
    SELECT * INTO validation_result
    FROM public.validate_budget_transfer(
        project_id_param,
        from_wbs_item_id,
        to_wbs_item_id,
        transfer_amount
    );

    -- If validation fails, return error
    IF NOT validation_result.is_valid THEN
        RETURN QUERY
        SELECT
            NULL::uuid as budget_transfer_id,
            false as is_valid,
            validation_result.error_message;
        RETURN;
    END IF;

    -- Create the budget transfer
    INSERT INTO public.budget_transfer (
        project_id,
        tender_line_item_id,
        from_wbs_library_item_id,
        to_wbs_library_item_id,
        transfer_amount,
        reason,
        created_by_user_id
    ) VALUES (
        project_id_param,
        line_item_id_param,
        from_wbs_item_id,
        to_wbs_item_id,
        transfer_amount,
        reason,
        auth.uid()
    ) RETURNING budget_transfer_id INTO new_transfer_id;

    -- Return success
    RETURN QUERY
    SELECT
        new_transfer_id,
        true as is_valid,
        NULL::text as error_message;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_tender_budget_transfer_data (project_id_param uuid, tender_id_param uuid) RETURNS TABLE (
	tender jsonb,
	wbs_items jsonb,
	budget_transfers jsonb
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	tender_json jsonb;
	wbs_items_json jsonb := '[]'::jsonb;
	budget_transfers_json jsonb := '[]'::jsonb;
	v_active_budget_version_id uuid;
BEGIN
	-- Verify project access and load tender data
	SELECT
		row_to_json(tender_row)::jsonb,
		p.active_budget_version_id
	INTO
		tender_json,
		v_active_budget_version_id
	FROM (
		SELECT
			t.tender_id,
			t.tender_name,
			t.project_id
		FROM public.tender t
		WHERE t.tender_id = tender_id_param
		AND t.project_id = project_id_param
	) tender_row
	JOIN public.project p ON p.project_id = project_id_param;

	-- Check if user has access to project
	IF NOT public.current_user_has_entity_role('project', project_id_param, 'viewer') THEN
		RAISE EXCEPTION 'Access denied to project';
	END IF;

	-- Check if tender was found
	IF tender_json IS NULL THEN
		RAISE EXCEPTION 'Tender not found or does not belong to project';
	END IF;

	-- Load WBS items with budget data for the active budget version
	IF v_active_budget_version_id IS NOT NULL THEN
		SELECT
			COALESCE(
				jsonb_agg(
					jsonb_build_object(
						'wbs_library_item_id', wli.wbs_library_item_id,
						'code', wli.code,
						'description', wli.description,
						'level', wli.level,
						'budget_amount', bi.budget_amount,
						'currency', jsonb_build_object(
							'currency_code', c.currency_code,
							'symbol', c.symbol,
							'symbol_position', c.symbol_position
						)
					)
					ORDER BY wli.code
				),
				'[]'::jsonb
			)
		INTO wbs_items_json
		FROM public.wbs_library_item wli
		LEFT JOIN public.budget_item bi ON wli.wbs_library_item_id = bi.wbs_library_item_id
			AND bi.budget_version_id = v_active_budget_version_id
		LEFT JOIN public.currency c ON bi.currency_code = c.currency_code
		WHERE wli.project_id = project_id_param
		AND wli.level = 4; -- Only level 4 items for budget transfers
	END IF;

	-- Load tender-level budget transfers (where tender_line_item_id IS NULL)
	SELECT
		COALESCE(
			jsonb_agg(
				jsonb_build_object(
					'budget_transfer_id', bt.budget_transfer_id,
					'project_id', bt.project_id,
					'tender_line_item_id', bt.tender_line_item_id,
					'from_wbs_library_item_id', bt.from_wbs_library_item_id,
					'to_wbs_library_item_id', bt.to_wbs_library_item_id,
					'transfer_amount', bt.transfer_amount,
					'reason', bt.reason,
					'created_at', bt.created_at,
					'updated_at', bt.updated_at,
					'from_wbs_item', jsonb_build_object(
						'code', from_wli.code,
						'description', from_wli.description
					),
					'to_wbs_item', jsonb_build_object(
						'code', to_wli.code,
						'description', to_wli.description
					)
				)
				ORDER BY bt.created_at DESC
			),
			'[]'::jsonb
		)
	INTO budget_transfers_json
	FROM public.budget_transfer bt
	JOIN public.wbs_library_item from_wli ON bt.from_wbs_library_item_id = from_wli.wbs_library_item_id
	JOIN public.wbs_library_item to_wli ON bt.to_wbs_library_item_id = to_wli.wbs_library_item_id
	WHERE bt.project_id = project_id_param
	AND bt.tender_line_item_id IS NULL; -- Only tender-level transfers

	-- Return the data
	RETURN QUERY
	SELECT
		tender_json,
		wbs_items_json,
		budget_transfers_json;
END;
$function$;
