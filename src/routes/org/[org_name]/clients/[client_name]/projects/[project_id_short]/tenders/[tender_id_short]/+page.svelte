<script lang="ts">
	import type { PageProps } from './$types';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import * as Card from '$lib/components/ui/card';
	import * as Tabs from '$lib/components/ui/tabs';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import EditIcon from 'phosphor-svelte/lib/PencilSimple';
	import ListIcon from 'phosphor-svelte/lib/List';
	import { formatCurrency } from '$lib/utils';

	const { data }: PageProps = $props();

	const tender = $derived(data.tender);
	const project = $derived(data.project);

	function getStatusColor(status: string) {
		switch (status) {
			case 'submitted':
				return 'bg-yellow-100 text-yellow-800';
			case 'under_review':
				return 'bg-blue-100 text-blue-800';
			case 'selected':
				return 'bg-green-100 text-green-800';
			case 'rejected':
				return 'bg-red-100 text-red-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	}
</script>

<svelte:head>
	<title>{tender?.tender_name} - {project?.name}</title>
</svelte:head>

<div class="space-y-6 p-4 lg:p-6">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold tracking-tight">{tender?.tender_name}</h1>
			<p class="text-muted-foreground">
				Tender from {tender?.vendor?.name} for {project?.name}
			</p>
		</div>
		<div class="flex items-center space-x-2">
			<Button
				variant="outline"
				href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
					data.client_name,
				)}/projects/{encodeURIComponent(data.project_id_short)}/tenders/{encodeURIComponent(
					data.tender_id_short,
				)}/edit"
			>
				<EditIcon class="mr-2 h-4 w-4" />
				Edit
			</Button>
			<Button
				href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
					data.client_name,
				)}/projects/{encodeURIComponent(data.project_id_short)}/tenders/{encodeURIComponent(
					data.tender_id_short,
				)}/line-items"
			>
				<ListIcon class="mr-2 h-4 w-4" />
				Manage Line Items
			</Button>
		</div>
	</div>

	<!-- Tender Overview -->
	<div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
		<Card.Root class="lg:col-span-2">
			<Card.Header>
				<Card.Title>Tender Details</Card.Title>
			</Card.Header>
			<Card.Content class="space-y-4">
				<dl class="grid grid-cols-2 gap-4">
					<div>
						<dt class="text-sm font-medium text-gray-500">Vendor</dt>
						<dd class="text-sm">{tender?.vendor?.name}</dd>
					</div>
					<div>
						<dt class="text-sm font-medium text-gray-500">Status</dt>
						<dd class="mt-1">
							<Badge class={getStatusColor(tender?.status || '')}>
								{tender?.status?.replace('_', ' ')}
							</Badge>
						</dd>
					</div>
					<div>
						<dt class="text-sm font-medium text-gray-500">Submission Date</dt>
						<dd class="text-sm">
							{tender?.submission_date
								? new Date(tender.submission_date).toLocaleDateString('en-US', {
										year: 'numeric',
										month: 'long',
										day: 'numeric',
									})
								: '-'}
						</dd>
					</div>
					<div>
						<dt class="text-sm font-medium text-gray-500">Currency</dt>
						<dd class="text-sm">{tender?.currency_code}</dd>
					</div>
					{#if tender?.description}
						<div class="col-span-2">
							<dt class="text-sm font-medium text-gray-500">Description</dt>
							<dd class="text-sm">{tender.description}</dd>
						</div>
					{/if}
					{#if tender?.notes}
						<div class="col-span-2">
							<dt class="text-sm font-medium text-gray-500">Notes</dt>
							<dd class="text-sm">{tender.notes}</dd>
						</div>
					{/if}
				</dl>
			</Card.Content>
		</Card.Root>

		<Card.Root>
			<Card.Header>
				<Card.Title>Summary</Card.Title>
			</Card.Header>
			<Card.Content>
				<dl class="space-y-4">
					<div>
						<dt class="text-sm font-medium text-gray-500">Total Amount</dt>
						<dd class="text-2xl font-bold">
							{formatCurrency(
								tender?.tender_revision?.[0]?.tender_line_item?.reduce(
									(sum: number, item) => sum + (item.subtotal || 0),
									0,
								) || 0,
								{
									symbol: tender?.vendor?.currency ?? '$',
									fallback: '-',
								},
							)}
						</dd>
					</div>
					<div>
						<dt class="text-sm font-medium text-gray-500">Line Items</dt>
						<dd class="text-lg font-semibold">
							{tender?.tender_revision?.[0]?.tender_line_item?.length || 0}
						</dd>
					</div>
					<div>
						<dt class="text-sm font-medium text-gray-500">WBS Mappings</dt>
						<dd class="text-lg font-semibold">
							{tender?.tender_revision?.[0]?.tender_line_item?.reduce(
								(sum: number, item) => sum + (item.tender_wbs_mapping?.length || 0),
								0,
							) || 0}
						</dd>
					</div>
				</dl>
			</Card.Content>
		</Card.Root>
	</div>

	<!-- Tabs for detailed information -->
	<Tabs.Root value="line-items" class="w-full">
		<Tabs.List class="grid w-full grid-cols-4">
			<Tabs.Trigger value="line-items">Line Items</Tabs.Trigger>
			<Tabs.Trigger value="revisions">Revisions</Tabs.Trigger>
			<Tabs.Trigger value="scoring">Scoring</Tabs.Trigger>
			<Tabs.Trigger value="budget-transfers">Budget Transfers</Tabs.Trigger>
		</Tabs.List>

		<Tabs.Content value="line-items" class="space-y-4">
			<div class="flex items-center justify-between">
				<h3 class="text-lg font-medium">Line Items</h3>
				<Button
					href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
						data.client_name,
					)}/projects/{encodeURIComponent(data.project_id_short)}/tenders/{encodeURIComponent(
						data.tender_id_short,
					)}/line-items"
				>
					Manage Line Items
				</Button>
			</div>
			{#if tender?.tender_revision?.[0]?.tender_line_item && tender.tender_revision[0].tender_line_item.length > 0}
				<div class="rounded-md border">
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Line #</TableHead>
								<TableHead>Description</TableHead>
								<TableHead>Quantity</TableHead>
								<TableHead>Unit</TableHead>
								<TableHead>Unit Rate</TableHead>
								<TableHead>Subtotal</TableHead>
								<TableHead>WBS Mappings</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{#each tender.tender_revision[0].tender_line_item as item (item.tender_line_item_id)}
								<TableRow>
									<TableCell>{item.line_number}</TableCell>
									<TableCell class="max-w-xs truncate">{item.description}</TableCell>
									<TableCell>{item.quantity || '-'}</TableCell>
									<TableCell>{item.unit || '-'}</TableCell>
									<TableCell>
										{item.unit_rate
											? formatCurrency(item.unit_rate, {
													symbol: tender?.vendor?.currency ?? '$',
													fallback: '-',
												})
											: '-'}
									</TableCell>
									<TableCell>
										{item.subtotal
											? formatCurrency(item.subtotal, {
													symbol: tender?.vendor?.currency ?? '$',
													fallback: '-',
												})
											: '-'}
									</TableCell>
									<TableCell>
										{item.tender_wbs_mapping?.length || 0} mapping{item.tender_wbs_mapping
											?.length === 1
											? ''
											: 's'}
									</TableCell>
								</TableRow>
							{/each}
						</TableBody>
					</Table>
				</div>
			{:else}
				<div class="py-8 text-center">
					<p class="text-gray-500">No line items added yet.</p>
					<Button
						class="mt-4"
						href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
							data.client_name,
						)}/projects/{encodeURIComponent(data.project_id_short)}/tenders/{encodeURIComponent(
							data.tender_id_short,
						)}/line-items"
					>
						Add Line Items
					</Button>
				</div>
			{/if}
		</Tabs.Content>

		<Tabs.Content value="revisions" class="space-y-4">
			<h3 class="text-lg font-medium">Revision History</h3>
			{#if tender?.tender_revision && tender.tender_revision.length > 0}
				<div class="space-y-4">
					{#each tender.tender_revision as revision, index (revision.tender_revision_id)}
						<Card.Root class={index === 0 ? 'border-blue-200 bg-blue-50' : ''}>
							<Card.Header>
								<div class="flex items-center justify-between">
									<div>
										<Card.Title class="text-lg">
											Revision {revision.revision_number}
											{#if index === 0}
												<Badge variant="secondary" class="ml-2">Current</Badge>
											{/if}
										</Card.Title>
										<Card.Description>
											Created on {new Date(revision.created_at).toLocaleDateString('en-US', {
												year: 'numeric',
												month: 'long',
												day: 'numeric',
												hour: '2-digit',
												minute: '2-digit',
											})}
										</Card.Description>
									</div>
									<div class="text-right">
										<p class="text-sm text-gray-500">Line Items</p>
										<p class="text-lg font-semibold">
											{revision.tender_line_item?.length || 0}
										</p>
									</div>
								</div>
							</Card.Header>
							<Card.Content>
								<div class="grid grid-cols-2 gap-4 text-sm">
									<div>
										<p class="text-gray-500">Total Amount</p>
										<p class="font-medium">
											{formatCurrency(
												revision.tender_line_item?.reduce(
													(sum, item) => sum + (item.subtotal || 0),
													0,
												) || 0,
												{
													symbol: tender?.vendor?.currency ?? '$',
													fallback: '-',
												},
											)}
										</p>
									</div>
									<div>
										<p class="text-gray-500">WBS Mappings</p>
										<p class="font-medium">
											{revision.tender_line_item?.reduce(
												(sum, item) => sum + (item.tender_wbs_mapping?.length || 0),
												0,
											) || 0}
										</p>
									</div>
								</div>
								{#if revision.revision_notes}
									<div class="mt-4">
										<p class="text-sm text-gray-500">Notes</p>
										<p class="text-sm">{revision.revision_notes}</p>
									</div>
								{/if}
							</Card.Content>
						</Card.Root>
					{/each}
				</div>
			{:else}
				<p class="text-gray-500">No revisions found.</p>
			{/if}
		</Tabs.Content>

		<Tabs.Content value="scoring" class="space-y-4">
			<h3 class="text-lg font-medium">Tender Scoring</h3>
			<p class="text-gray-500">Scoring functionality coming soon.</p>
		</Tabs.Content>

		<Tabs.Content value="budget-transfers" class="space-y-4">
			<div class="flex items-center justify-between">
				<h3 class="text-lg font-medium">Budget Transfers</h3>
				<Button
					href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
						data.client_name,
					)}/projects/{encodeURIComponent(data.project_id_short)}/tenders/{encodeURIComponent(
						data.tender_id_short,
					)}/budget-transfers"
				>
					Manage Budget Transfers
				</Button>
			</div>
			<p class="text-gray-500">
				Budget transfers allow you to reallocate budget between WBS items for tender analysis.
			</p>
		</Tabs.Content>
	</Tabs.Root>
</div>
