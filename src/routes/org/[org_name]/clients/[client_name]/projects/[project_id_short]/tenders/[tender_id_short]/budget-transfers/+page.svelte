<script lang="ts">
	import type { PageProps } from './$types';
	import * as Card from '$lib/components/ui/card';
	import BudgetTransferInterface from '$lib/components/tender/BudgetTransferInterface.svelte';
	import { enhance } from '$app/forms';
	import { toast } from 'svelte-sonner';

	const { data }: PageProps = $props();

	// Type assertions for the RPC function return data
	const tender = $derived(data.tender as any);
	const wbsItems = $derived((data.wbsItems as any[]) || []);
	const budgetTransfers = $derived(
		((data.budgetTransfers as any[]) || []).map((transfer: any) => ({
			...transfer,
			reason: transfer.reason || 'No reason provided',
			from_wbs_item: transfer.from_wbs_item
				? {
						code: transfer.from_wbs_item.code || '',
						description: transfer.from_wbs_item.description || '',
					}
				: undefined,
			to_wbs_item: transfer.to_wbs_item
				? {
						code: transfer.to_wbs_item.code || '',
						description: transfer.to_wbs_item.description || '',
					}
				: undefined,
		})),
	);

	// Get currency information from the first WBS item (they should all have the same currency)
	const currencyInfo = $derived.by(() => {
		const firstItem = wbsItems[0];
		if (firstItem?.currency) {
			return {
				symbol: firstItem.currency.symbol || 'kr',
				symbolPosition: (firstItem.currency.symbol_position as 'before' | 'after') || 'after',
			};
		}
		return { symbol: 'kr', symbolPosition: 'after' as const };
	});

	function handleTransferCreate(payload: {
		transfer: {
			from_wbs_library_item_id: string;
			to_wbs_library_item_id: string;
			transfer_amount: number;
			reason: string;
		};
	}) {
		// Submit the form programmatically
		const form = document.querySelector('form[action="?/createTransfer"]') as HTMLFormElement;
		if (form) {
			// Populate hidden form fields
			const formData = new FormData();
			formData.append('from_wbs_library_item_id', payload.transfer.from_wbs_library_item_id);
			formData.append('to_wbs_library_item_id', payload.transfer.to_wbs_library_item_id);
			formData.append('transfer_amount', payload.transfer.transfer_amount.toString());
			formData.append('transfer_reason', payload.transfer.reason);

			// Submit the form
			fetch(form.action, {
				method: 'POST',
				body: formData,
			}).then(() => {
				// Reload the page to show updated data
				window.location.reload();
			});
		}
	}

	function handleTransferValidate(payload: {
		from_wbs_library_item_id: string;
		to_wbs_library_item_id: string;
		transfer_amount: number;
	}) {
		// Submit validation form programmatically
		const form = document.querySelector('form[action="?/validateTransfer"]') as HTMLFormElement;
		if (form) {
			const formData = new FormData();
			formData.append('from_wbs_library_item_id', payload.from_wbs_library_item_id);
			formData.append('to_wbs_library_item_id', payload.to_wbs_library_item_id);
			formData.append('transfer_amount', payload.transfer_amount.toString());

			fetch(form.action, {
				method: 'POST',
				body: formData,
			});
		}
	}
</script>

<svelte:head>
	<title>Budget Transfers - {tender?.tender_name} - {data.project?.name}</title>
</svelte:head>

<div class="space-y-6 p-4 lg:p-6">
	<h1 class="sr-only">Budget Transfers</h1>

	<!-- Header -->
	<div class="space-y-2">
		<div class="flex items-center justify-between">
			<div>
				<h1 class="text-2xl font-bold tracking-tight">Budget Transfers</h1>
				<p class="text-muted-foreground">
					Manage budget transfers between WBS items for {tender?.tender_name}
				</p>
			</div>
		</div>

		<!-- Tender Summary -->
		<Card.Root>
			<Card.Content class="p-4">
				<div class="grid grid-cols-1 gap-4 md:grid-cols-3">
					<div>
						<p class="text-sm text-gray-500">Tender</p>
						<p class="font-medium">{tender?.tender_name}</p>
					</div>
					<div>
						<p class="text-sm text-gray-500">Available WBS Items</p>
						<p class="font-medium">{wbsItems.length} items</p>
					</div>
					<div>
						<p class="text-sm text-gray-500">Total Transfers</p>
						<p class="font-medium">{budgetTransfers.length} transfers</p>
					</div>
				</div>
			</Card.Content>
		</Card.Root>
	</div>

	<!-- Budget Transfer Interface -->
	<BudgetTransferInterface
		availableWbsItems={wbsItems}
		existingTransfers={budgetTransfers}
		currencySymbol={currencyInfo.symbol}
		symbolPosition={currencyInfo.symbolPosition}
		onTransferCreate={handleTransferCreate}
		onTransferValidate={handleTransferValidate}
	/>
</div>

<!-- Create Transfer Form -->
<form
	method="POST"
	action="?/createTransfer"
	use:enhance={() => {
		return ({ result, update }) => {
			if (result.type === 'success') {
				toast.success('Budget transfer created successfully');
			} else if (result.type === 'failure') {
				toast.error('Failed to create budget transfer');
			}
			update();
		};
	}}
>
	<!-- Form fields will be populated by JavaScript -->
</form>

<!-- Validate Transfer Form -->
<form method="POST" action="?/validateTransfer" use:enhance>
	<!-- Form fields will be populated by JavaScript -->
</form>
