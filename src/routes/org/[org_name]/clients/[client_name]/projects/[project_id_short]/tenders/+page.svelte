<script lang="ts">
	import type { PageProps } from './$types';
	import type { ProjectTendersData } from '$lib/schemas/tender';
	import { Button } from '$lib/components/ui/button';
	import PlusIcon from 'phosphor-svelte/lib/Plus';
	import DotsThreeVerticalIcon from 'phosphor-svelte/lib/DotsThreeVertical';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { goto, invalidate } from '$app/navigation';
	import { tenderShortId } from '$lib/schemas/project';
	import { formatCurrency } from '$lib/utils';
	import ConfirmationDialog from '$lib/components/tender/ConfirmationDialog.svelte';
	import { enhance } from '$app/forms';

	const { data }: PageProps = $props();

	const project = $derived(data.project);
	type TenderWithCurrencyPosition = ProjectTendersData[number] & {
		symbol_position?: string | null;
	};

	const tenders = $derived((data.tenders ?? []) as TenderWithCurrencyPosition[]);

	let deleteDialogOpen = $state(false);
	let tenderToDelete = $state<string | null>(null);

	function openDeleteDialog(tenderId: string) {
		tenderToDelete = tenderId;
		deleteDialogOpen = true;
	}

	function handleDeleteCancel() {
		deleteDialogOpen = false;
		tenderToDelete = null;
	}
</script>

<svelte:head>
	<title>Tenders - {project?.name}</title>
</svelte:head>

<div class="space-y-6 p-4 lg:p-6">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold tracking-tight">Tenders</h1>
			<p class="text-muted-foreground">
				Manage tender submissions and analysis for {project?.name}
			</p>
		</div>
		<Button
			href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
				data.client_name,
			)}/projects/{encodeURIComponent(data.project_id_short)}/tenders/new"
		>
			<PlusIcon class="mr-2 h-4 w-4" />
			New Tender
		</Button>
	</div>

	{#if tenders && tenders.length > 0}
		<div class="rounded-md border">
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>Tender Name</TableHead>
						<TableHead>Vendor</TableHead>
						<TableHead>Status</TableHead>
						<TableHead>Submission Date</TableHead>
						<TableHead>Total Amount</TableHead>
						<TableHead>Line Items</TableHead>
						<TableHead class="text-right">Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{#each tenders as tender (tender.tender_id)}
						<TableRow>
							<TableCell class="font-medium">
								<a
									href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
										data.client_name,
									)}/projects/{encodeURIComponent(data.project_id_short)}/tenders/{tenderShortId(
										tender.tender_id,
									)}"
									class="underline"
								>
									{tender.tender_name}
								</a>
							</TableCell>
							<TableCell>{tender.vendor_name}</TableCell>
							<TableCell>
								<span
									class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium"
									class:bg-yellow-100={tender.status === 'submitted'}
									class:text-yellow-800={tender.status === 'submitted'}
									class:bg-blue-100={tender.status === 'under_review'}
									class:text-blue-800={tender.status === 'under_review'}
									class:bg-green-100={tender.status === 'selected'}
									class:text-green-800={tender.status === 'selected'}
									class:bg-red-100={tender.status === 'rejected'}
									class:text-red-800={tender.status === 'rejected'}
								>
									{tender.status.replace('_', ' ')}
								</span>
							</TableCell>
							<TableCell>
								{new Date(tender.submission_date).toLocaleDateString('en-US', {
									year: 'numeric',
									month: 'short',
									day: 'numeric',
								})}
							</TableCell>
							<TableCell>
								{formatCurrency(tender.total_amount, {
									symbol: tender.currency_symbol ?? undefined,
									symbolPosition:
										tender.symbol_position === 'after' || tender.symbol_position === 'right'
											? 'after'
											: 'before',
									fallback: '-',
								})}
							</TableCell>
							<TableCell>{tender.line_item_count}</TableCell>
							<TableCell class="text-right">
								<DropdownMenu.Root>
									<DropdownMenu.Trigger>
										{#snippet child({ props })}
											<Button variant="ghost" {...props} size="sm">
												<DotsThreeVerticalIcon class="h-4 w-4" />
												<span class="sr-only">Open menu</span>
											</Button>
										{/snippet}
									</DropdownMenu.Trigger>
									<DropdownMenu.Content align="end">
										<a
											href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
												data.client_name,
											)}/projects/{encodeURIComponent(
												data.project_id_short,
											)}/tenders/{tenderShortId(tender.tender_id)}"
										>
											<DropdownMenu.Item>View</DropdownMenu.Item>
										</a>
										<a
											href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
												data.client_name,
											)}/projects/{encodeURIComponent(
												data.project_id_short,
											)}/tenders/{tenderShortId(tender.tender_id)}/edit"
										>
											<DropdownMenu.Item>Edit</DropdownMenu.Item>
										</a>
										<DropdownMenu.Separator />
										<DropdownMenu.Item
											class="text-red-600"
											onclick={() => openDeleteDialog(tender.tender_id)}
										>
											Delete
										</DropdownMenu.Item>
									</DropdownMenu.Content>
								</DropdownMenu.Root>
							</TableCell>
						</TableRow>
					{/each}
				</TableBody>
			</Table>
		</div>
	{:else}
		<div class="py-12 text-center">
			<h3 class="mt-2 text-sm font-semibold text-gray-900">No tenders</h3>
			<p class="mt-1 text-sm text-gray-500">Get started by creating a new tender.</p>
			<div class="mt-6">
				<Button
					href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
						data.client_name,
					)}/projects/{encodeURIComponent(data.project_id_short)}/tenders/new"
				>
					<PlusIcon class="mr-2 h-4 w-4" />
					New Tender
				</Button>
			</div>
		</div>
	{/if}
</div>

<!-- Delete Confirmation Dialog -->
<ConfirmationDialog
	open={deleteDialogOpen}
	title="Delete Tender"
	description="Are you sure you want to delete this tender? This action cannot be undone and will remove all associated line items and mappings."
	confirmText="Delete"
	variant="destructive"
	onCancel={handleDeleteCancel}
>
	{#snippet formContent()}
		<form
			method="POST"
			action="?/delete"
			use:enhance={() => {
				return ({ update }) => {
					deleteDialogOpen = false;
					tenderToDelete = null;
					invalidate('project:tenders');
					update();
				};
			}}
		>
			<input type="hidden" name="tender_id" value={tenderToDelete} />
			<div class="flex gap-3 pt-4">
				<Button variant="outline" type="button" onclick={handleDeleteCancel}>Cancel</Button>
				<Button variant="destructive" type="submit">Delete</Button>
			</div>
		</form>
	{/snippet}
</ConfirmationDialog>
