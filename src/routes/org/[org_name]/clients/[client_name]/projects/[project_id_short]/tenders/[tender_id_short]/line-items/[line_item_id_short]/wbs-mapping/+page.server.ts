import { error, fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import {
	createWbsMappingSchema,
	editWbsMappingSchema,
	type TenderLineItemWithMappings,
	type WbsItemWithBudget,
} from '$lib/schemas/tender';
import { tenderLineItemUUID, projectUUID } from '$lib/schemas/project';
import { createWbsMapping, updateWbsMapping, deleteWbsMapping } from '$lib/tender_utils';
import type { PageServerLoad, Actions } from './$types';

type WbsMappingProject = {
	project_id: string;
	name: string;
	active_budget_version_id: string | null;
};

type BudgetTransferWithItems = {
	budget_transfer_id: string;
	project_id: string;
	tender_line_item_id: string | null;
	from_wbs_library_item_id: string;
	to_wbs_library_item_id: string;
	transfer_amount: number;
	reason: string | null;
	created_at: string;
	updated_at: string;
	from_wbs_item: { code: string | null; description: string | null } | null;
	to_wbs_item: { code: string | null; description: string | null } | null;
};

type TenderWbsMappingPayload = {
	project: WbsMappingProject;
	line_item: TenderLineItemWithMappings;
	wbs_items: WbsItemWithBudget[];
	budget_transfers: BudgetTransferWithItems[];
};

export const load: PageServerLoad = async ({ params, locals: { supabase, user } }) => {
	if (!user) {
		throw redirect(302, '/auth/signin');
	}

	const projectId = projectUUID(params.project_id_short);
	const lineItemId = tenderLineItemUUID(params.line_item_id_short);
	try {
		const { data, error: rpcError } = await supabase.rpc('get_tender_wbs_mapping_data', {
			project_id_param: projectId,
			tender_line_item_id_param: lineItemId,
		});

		if (rpcError) {
			console.error('Error fetching WBS mapping data:', rpcError);
			throw error(500, 'Failed to load WBS mapping data');
		}

		const payload = (data?.[0] ?? null) as TenderWbsMappingPayload | null;
		if (!payload) {
			throw error(404, 'WBS mapping data not found');
		}

		const {
			project,
			line_item: lineItem,
			wbs_items: wbsItems,
			budget_transfers: budgetTransfers,
		} = payload;

		if (!project) {
			throw error(404, 'Project not found');
		}

		// Initialize forms
		const createForm = await superValidate(zod(createWbsMappingSchema));
		const editForm = await superValidate(zod(editWbsMappingSchema));

		return {
			lineItem,
			wbsItems,
			budgetTransfers,
			project,
			createForm,
			editForm,
		};
	} catch (err) {
		console.error('Error loading WBS mapping page:', err);
		throw error(500, 'Failed to load page data');
	}
};

export const actions: Actions = {
	createMapping: async ({ request, params, locals: { supabase, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const lineItemId = tenderLineItemUUID(params.line_item_id_short);
		const form = await superValidate(request, zod(createWbsMappingSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			await createWbsMapping(supabase, lineItemId, {
				wbs_library_item_id: form.data.wbs_library_item_id,
				coverage_percentage: form.data.coverage_percentage,
				coverage_quantity: form.data.coverage_quantity,
				notes: form.data.notes,
			});

			return {
				form,
				message: { type: 'success', text: 'WBS mapping created successfully' },
			};
		} catch (err) {
			console.error('Error creating WBS mapping:', err);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create WBS mapping' },
			});
		}
	},

	updateMapping: async ({ request, locals: { supabase, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const form = await superValidate(request, zod(editWbsMappingSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const formData = await request.formData();
		const mappingId = formData.get('mapping_id') as string;

		if (!mappingId) {
			return fail(400, {
				form,
				message: { type: 'error', text: 'Mapping ID is required' },
			});
		}

		try {
			await updateWbsMapping(supabase, mappingId, {
				coverage_percentage: form.data.coverage_percentage,
				coverage_quantity: form.data.coverage_quantity,
				notes: form.data.notes,
			});

			return {
				form,
				message: { type: 'success', text: 'WBS mapping updated successfully' },
			};
		} catch (err) {
			console.error('Error updating WBS mapping:', err);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to update WBS mapping' },
			});
		}
	},

	deleteMapping: async ({ request, locals: { supabase, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const formData = await request.formData();
		const mappingId = formData.get('mapping_id') as string;

		if (!mappingId) {
			return fail(400, {
				message: { type: 'error', text: 'Mapping ID is required' },
			});
		}

		try {
			await deleteWbsMapping(supabase, mappingId);

			return {
				message: { type: 'success', text: 'WBS mapping deleted successfully' },
			};
		} catch (err) {
			console.error('Error deleting WBS mapping:', err);
			return fail(500, {
				message: { type: 'error', text: 'Failed to delete WBS mapping' },
			});
		}
	},
};
