<script lang="ts">
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Button } from '$lib/components/ui/button';
	import { Slider } from '$lib/components/ui/slider';
	import * as RadioGroup from '$lib/components/ui/radio-group';
	import { Badge } from '$lib/components/ui/badge';
	import { formatCurrency } from '$lib/utils';

	interface Props {
		coverageType: 'percentage' | 'quantity';
		coveragePercentage: number;
		coverageQuantity: number | null;
		budgetQuantity?: number | null;
		budgetAmount?: number | null;
		unit?: string | null;
		currencySymbol?: string;
		symbolPosition?: 'before' | 'after';
		disabled?: boolean;
		showCalculatedAmount?: boolean;
		onChange?: (payload: {
			type: 'percentage' | 'quantity';
			percentage: number;
			quantity: number | null;
		}) => void;
	}

	let {
		coverageType = $bindable('percentage'),
		coveragePercentage = $bindable(100),
		coverageQuantity = $bindable(null),
		budgetQuantity = null,
		budgetAmount = null,
		unit = null,
		currencySymbol = 'kr',
		symbolPosition = 'after',
		disabled = false,
		showCalculatedAmount = true,
		onChange = () => {},
	}: Props = $props();

	// Calculate coverage amount based on budget and coverage
	const calculatedAmount = $derived.by(() => {
		if (!budgetAmount) return null;

		if (coverageType === 'percentage') {
			return (budgetAmount * coveragePercentage) / 100;
		} else if (coverageType === 'quantity' && coverageQuantity && budgetQuantity) {
			return (budgetAmount * coverageQuantity) / budgetQuantity;
		}

		return null;
	});

	// Calculate percentage from quantity
	const calculatedPercentage = $derived.by(() => {
		if (coverageType === 'quantity' && coverageQuantity && budgetQuantity) {
			return Math.min(100, (coverageQuantity / budgetQuantity) * 100);
		}
		return coveragePercentage;
	});

	// Validation states
	const isOverAllocated = $derived.by(() => {
		if (coverageType === 'percentage') {
			return coveragePercentage > 100;
		} else if (coverageType === 'quantity' && coverageQuantity && budgetQuantity) {
			return coverageQuantity > budgetQuantity;
		}
		return false;
	});

	const isUnderAllocated = $derived.by(() => {
		if (coverageType === 'percentage') {
			return coveragePercentage < 100;
		} else if (coverageType === 'quantity' && coverageQuantity && budgetQuantity) {
			return coverageQuantity < budgetQuantity;
		}
		return false;
	});

	function handleTypeChange(newType: string) {
		if (newType !== 'percentage' && newType !== 'quantity') return;

		coverageType = newType as 'percentage' | 'quantity';

		// Auto-convert between types when switching
		if (newType === 'quantity' && budgetQuantity && coverageType === 'percentage') {
			coverageQuantity = Math.round((budgetQuantity * coveragePercentage) / 100);
		} else if (newType === 'percentage' && coverageQuantity && budgetQuantity) {
			coveragePercentage = Math.min(100, (coverageQuantity / budgetQuantity) * 100);
		}

		emitChange();
	}

	function handlePercentageChange(value: number) {
		coveragePercentage = Math.max(0, Math.min(200, value)); // Allow up to 200% for over-allocation
		emitChange();
	}

	function handleQuantityChange(value: number | null) {
		coverageQuantity = value && value > 0 ? value : null;
		emitChange();
	}

	function setFullCoverage() {
		if (coverageType === 'percentage') {
			coveragePercentage = 100;
		} else if (budgetQuantity) {
			coverageQuantity = budgetQuantity;
		}
		emitChange();
	}

	function emitChange() {
		onChange({
			type: coverageType,
			percentage: coveragePercentage,
			quantity: coverageQuantity,
		});
	}
</script>

<div class="space-y-4">
	<!-- Coverage Type Selection -->
	<div class="space-y-2">
		<Label class="text-sm font-medium">Coverage Method</Label>
		<RadioGroup.Root value={coverageType} onValueChange={handleTypeChange} {disabled}>
			<div class="flex items-center space-x-2">
				<RadioGroup.Item value="percentage" id="percentage" />
				<Label for="percentage" class="text-sm">Percentage of Budget</Label>
			</div>
			<div class="flex items-center space-x-2">
				<RadioGroup.Item value="quantity" id="quantity" />
				<Label for="quantity" class="text-sm">
					Quantity Coverage
					{#if unit}
						({unit})
					{/if}
				</Label>
			</div>
		</RadioGroup.Root>
	</div>

	<!-- Percentage Input -->
	{#if coverageType === 'percentage'}
		<div class="space-y-3">
			<div class="flex items-center justify-between">
				<Label for="percentage-input" class="text-sm font-medium">Coverage Percentage</Label>
				<div class="flex items-center space-x-2">
					<Badge
						variant={isOverAllocated ? 'destructive' : isUnderAllocated ? 'secondary' : 'default'}
					>
						{coveragePercentage.toFixed(1)}%
					</Badge>
					<Button
						variant="outline"
						size="sm"
						onclick={setFullCoverage}
						{disabled}
						class="h-auto px-2 py-1 text-xs"
					>
						100%
					</Button>
				</div>
			</div>

			<!-- Slider -->
			<Slider
				value={coveragePercentage}
				onValueChange={(value) => handlePercentageChange(value)}
				max={150}
				min={0}
				step={1}
				{disabled}
				type="single"
				class="w-full"
			/>

			<!-- Numeric Input -->
			<div class="flex items-center space-x-2">
				<Input
					id="percentage-input"
					type="number"
					min="0"
					max="200"
					step="0.1"
					value={coveragePercentage}
					onchange={(e) => handlePercentageChange(parseFloat(e.currentTarget.value) || 0)}
					{disabled}
					class="w-24"
				/>
				<span class="text-sm text-gray-500">%</span>
			</div>
		</div>
	{/if}

	<!-- Quantity Input -->
	{#if coverageType === 'quantity'}
		<div class="space-y-3">
			<div class="flex items-center justify-between">
				<Label for="quantity-input" class="text-sm font-medium">
					Coverage Quantity
					{#if unit}
						({unit})
					{/if}
				</Label>
				<div class="flex items-center space-x-2">
					{#if budgetQuantity}
						<Badge
							variant={isOverAllocated ? 'destructive' : isUnderAllocated ? 'secondary' : 'default'}
						>
							{coverageQuantity || 0} / {budgetQuantity}
						</Badge>
						<Button
							variant="outline"
							size="sm"
							onclick={setFullCoverage}
							{disabled}
							class="h-auto px-2 py-1 text-xs"
						>
							Full
						</Button>
					{/if}
				</div>
			</div>

			<div class="flex items-center space-x-2">
				<Input
					id="quantity-input"
					type="number"
					min="0"
					step="0.0001"
					value={coverageQuantity}
					onchange={(e) => handleQuantityChange(parseFloat(e.currentTarget.value) || null)}
					{disabled}
					class="flex-1"
				/>
				{#if unit}
					<span class="text-sm text-gray-500">{unit}</span>
				{/if}
			</div>

			{#if budgetQuantity}
				<div class="text-xs text-gray-500">
					Budget quantity: {budgetQuantity}
					{unit || ''}
					{#if calculatedPercentage}
						• {calculatedPercentage.toFixed(1)}% coverage
					{/if}
				</div>
			{/if}
		</div>
	{/if}

	<!-- Budget Information -->
	{#if budgetAmount && showCalculatedAmount}
		<div class="space-y-1 rounded-md bg-gray-50 p-3">
			<div class="text-sm font-medium text-gray-700">Budget Information</div>
			<div class="space-y-1 text-xs text-gray-600">
				<div>
					Total Budget: {formatCurrency(budgetAmount, {
						symbol: currencySymbol,
						symbolPosition,
						fallback: '-',
					})}
				</div>
				{#if calculatedAmount}
					<div class="font-medium">
						Coverage Amount: {formatCurrency(calculatedAmount, {
							symbol: currencySymbol,
							symbolPosition,
							fallback: '-',
						})}
					</div>
				{/if}
			</div>
		</div>
	{/if}

	<!-- Validation Messages -->
	{#if isOverAllocated}
		<div class="rounded bg-red-50 p-2 text-xs text-red-600">
			⚠️ Coverage exceeds budget allocation. This may require budget transfers.
		</div>
	{:else if isUnderAllocated && coverageType === 'percentage' && coveragePercentage < 50}
		<div class="rounded bg-amber-50 p-2 text-xs text-amber-600">
			ℹ️ Partial coverage detected. Consider if full coverage is needed.
		</div>
	{/if}
</div>
