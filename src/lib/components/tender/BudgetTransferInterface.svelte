<script lang="ts">
	import { Button, buttonVariants } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Card from '$lib/components/ui/card';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import { Badge } from '$lib/components/ui/badge';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import ArrowRightIcon from 'phosphor-svelte/lib/ArrowRight';
	import ExclamationTriangleIcon from 'phosphor-svelte/lib/WarningCircle';
	import CheckCircleIcon from 'phosphor-svelte/lib/CheckCircle';
	import CaretUpDownIcon from 'phosphor-svelte/lib/CaretUpDown';
	import CheckIcon from 'phosphor-svelte/lib/Check';
	import type { WbsItemWithBudget } from '$lib/schemas/tender';
	import { cn, formatCurrency } from '$lib/utils';
	import { SvelteMap } from 'svelte/reactivity';
	import { tick } from 'svelte';
	import { useId } from 'bits-ui';

	interface BudgetTransfer {
		budget_transfer_id: string;
		from_wbs_library_item_id: string;
		to_wbs_library_item_id: string;
		transfer_amount: number;
		reason: string;
		created_at: string;
		from_wbs_item?: { code: string; description: string };
		to_wbs_item?: { code: string; description: string };
	}

	interface Props {
		availableWbsItems: WbsItemWithBudget[];
		existingTransfers: BudgetTransfer[];
		currencySymbol?: string;
		symbolPosition?: 'before' | 'after';
		disabled?: boolean;
		onTransferCreate?: (payload: {
			transfer: {
				from_wbs_library_item_id: string;
				to_wbs_library_item_id: string;
				transfer_amount: number;
				reason: string;
			};
		}) => void;
		onTransferValidate?: (payload: {
			from_wbs_library_item_id: string;
			to_wbs_library_item_id: string;
			transfer_amount: number;
		}) => void;
	}

	let {
		availableWbsItems,
		existingTransfers = [],
		currencySymbol = 'kr',
		symbolPosition = 'after',
		disabled = false,
		onTransferCreate = () => {},
		onTransferValidate = () => {},
	}: Props = $props();

	let createDialogOpen = $state(false);
	let validationResult = $state<{
		is_valid: boolean;
		error_message?: string;
		from_available_amount?: number;
		to_current_amount?: number;
	} | null>(null);

	// Combobox popover control state
	let fromWbsOpen = $state(false);
	let toWbsOpen = $state(false);
	const fromTriggerId = useId();
	const toTriggerId = useId();

	function closeAndFocusTrigger(triggerId: string, setOpen: (value: boolean) => void) {
		setOpen(false);
		tick().then(() => {
			document.getElementById(triggerId)?.focus();
		});
	}

	// Form state for new transfer
	let transferForm = $state({
		from_wbs_library_item_id: '',
		to_wbs_library_item_id: '',
		transfer_amount: 0,
		reason: '',
	});

	// Calculate available amounts considering existing transfers
	const wbsBalances = $derived.by(() => {
		const balances = new SvelteMap<string, number>();

		// Initialize with budget amounts
		availableWbsItems.forEach((item) => {
			if (item.budget_amount) {
				balances.set(item.wbs_library_item_id, item.budget_amount);
			}
		});

		// Apply existing transfers
		existingTransfers.forEach((transfer) => {
			const fromBalance = balances.get(transfer.from_wbs_library_item_id) || 0;
			const toBalance = balances.get(transfer.to_wbs_library_item_id) || 0;

			balances.set(transfer.from_wbs_library_item_id, fromBalance - transfer.transfer_amount);
			balances.set(transfer.to_wbs_library_item_id, toBalance + transfer.transfer_amount);
		});

		return balances;
	});

	// Get WBS item by ID
	function getWbsItem(itemId: string): WbsItemWithBudget | undefined {
		return availableWbsItems.find((item) => item.wbs_library_item_id === itemId);
	}

	// Get available balance for WBS item
	function getAvailableBalance(itemId: string): number {
		return wbsBalances.get(itemId) || 0;
	}

	function openCreateDialog() {
		transferForm = {
			from_wbs_library_item_id: '',
			to_wbs_library_item_id: '',
			transfer_amount: 0,
			reason: '',
		};
		validationResult = null;
		createDialogOpen = true;
	}

	async function validateTransfer() {
		if (
			!transferForm.from_wbs_library_item_id ||
			!transferForm.to_wbs_library_item_id ||
			transferForm.transfer_amount <= 0
		) {
			return;
		}

		onTransferValidate({
			from_wbs_library_item_id: transferForm.from_wbs_library_item_id,
			to_wbs_library_item_id: transferForm.to_wbs_library_item_id,
			transfer_amount: transferForm.transfer_amount,
		});
	}

	function createTransfer() {
		if (!validationResult?.is_valid) return;

		const transfer = {
			from_wbs_library_item_id: transferForm.from_wbs_library_item_id,
			to_wbs_library_item_id: transferForm.to_wbs_library_item_id,
			transfer_amount: transferForm.transfer_amount,
			reason: transferForm.reason,
		};

		onTransferCreate({ transfer });
		createDialogOpen = false;
	}

	// Watch for form changes to trigger validation
	$effect(() => {
		if (
			transferForm.from_wbs_library_item_id &&
			transferForm.to_wbs_library_item_id &&
			transferForm.transfer_amount > 0
		) {
			validateTransfer();
		} else {
			validationResult = null;
		}
	});

	// WBS select options
	const wbsOptions = $derived.by(() =>
		availableWbsItems.map((item) => ({
			value: item.wbs_library_item_id,
			label: `${item.code}: ${item.description}`,
		})),
	);
</script>

<div class="space-y-4">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h3 class="text-lg font-semibold">Budget Transfers</h3>
			<p class="text-sm text-gray-600">Manage budget transfers between WBS items</p>
		</div>
		<Button onclick={openCreateDialog} {disabled}>
			<ArrowRightIcon class="mr-2 h-4 w-4" />
			Create Transfer
		</Button>
	</div>

	<!-- Existing Transfers -->
	{#if existingTransfers.length > 0}
		<div class="space-y-3">
			{#each existingTransfers as transfer (transfer.budget_transfer_id)}
				{@const fromItem = getWbsItem(transfer.from_wbs_library_item_id)}
				{@const toItem = getWbsItem(transfer.to_wbs_library_item_id)}

				<Card.Root>
					<Card.Content class="p-4">
						<div class="flex items-center justify-between">
							<div class="flex items-center space-x-4">
								<div class="text-center">
									<div class="font-mono text-sm font-medium">{fromItem?.code || 'Unknown'}</div>
									<div class="text-xs text-gray-500">From</div>
								</div>

								<ArrowRightIcon class="h-4 w-4 text-gray-400" />

								<div class="text-center">
									<div class="font-mono text-sm font-medium">{toItem?.code || 'Unknown'}</div>
									<div class="text-xs text-gray-500">To</div>
								</div>

								<div class="text-center">
									<div class="font-semibold">
										{formatCurrency(transfer.transfer_amount, {
											symbol: currencySymbol,
											symbolPosition,
											fallback: '-',
										})}
									</div>
									<div class="text-xs text-gray-500">Amount</div>
								</div>
							</div>

							<div class="text-right">
								<div class="text-xs text-gray-500">
									{new Date(transfer.created_at).toLocaleDateString()}
								</div>
								{#if transfer.reason}
									<div class="max-w-xs truncate text-xs text-gray-600" title={transfer.reason}>
										{transfer.reason}
									</div>
								{/if}
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	{:else}
		<div class="py-8 text-center text-gray-500">
			<p class="text-sm">No budget transfers yet</p>
			<p class="text-xs">Create transfers to reallocate budget between WBS items</p>
		</div>
	{/if}

	<!-- WBS Balances Summary -->
	{#if availableWbsItems.length > 0}
		<Card.Root>
			<Card.Header>
				<Card.Title class="text-base">Current WBS Balances</Card.Title>
			</Card.Header>
			<Card.Content>
				<div class="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3">
					{#each availableWbsItems as item (item.wbs_library_item_id)}
						{@const originalAmount = item.budget_amount || 0}
						{@const currentBalance = getAvailableBalance(item.wbs_library_item_id)}
						{@const hasChanged = Math.abs(originalAmount - currentBalance) > 0.01}

						<div class="rounded-md border p-3 {hasChanged ? 'border-blue-200 bg-blue-50' : ''}">
							<div class="font-mono text-sm font-medium">{item.code}</div>
							<div class="truncate text-xs text-gray-600">{item.description}</div>
							<div class="mt-1 space-y-1">
								<div class="text-sm">
									{formatCurrency(currentBalance, {
										symbol: currencySymbol,
										symbolPosition,
										fallback: '-',
									})}
								</div>
								{#if hasChanged}
									<div class="text-xs text-gray-500">
										Original: {formatCurrency(originalAmount, {
											symbol: currencySymbol,
											symbolPosition,
											fallback: '-',
										})}
									</div>
								{/if}
							</div>
						</div>
					{/each}
				</div>
			</Card.Content>
		</Card.Root>
	{/if}
</div>

<!-- Create Transfer Dialog -->
<Dialog.Root bind:open={createDialogOpen}>
	<Dialog.Content class="max-w-2xl">
		<Dialog.Header>
			<Dialog.Title>Create Budget Transfer</Dialog.Title>
			<Dialog.Description>Transfer budget amount between WBS items</Dialog.Description>
		</Dialog.Header>

		<div class="space-y-4 py-4">
			<!-- From WBS Item -->
			<div class="space-y-2">
				<Label class="text-sm font-medium">From WBS Item</Label>
				<Popover.Root bind:open={fromWbsOpen}>
					<Popover.Trigger
						id={fromTriggerId}
						role="combobox"
						class={cn(
							buttonVariants({ variant: 'outline' }),
							'w-full justify-between',
							!transferForm.from_wbs_library_item_id && 'text-muted-foreground',
						)}
						aria-expanded={fromWbsOpen}
					>
						<span class="truncate">
							{transferForm.from_wbs_library_item_id
								? wbsOptions.find((opt) => opt.value === transferForm.from_wbs_library_item_id)
										?.label
								: 'Select source WBS item'}
						</span>
						<CaretUpDownIcon class="ml-2 size-4 shrink-0 opacity-50" />
					</Popover.Trigger>
					<Popover.Content class="w-[--radix-popover-trigger-width] p-0" align="start">
						<Command.Root>
							<Command.Input autofocus placeholder="Search WBS items..." class="h-9" />
							<Command.Empty>No matching items found.</Command.Empty>
							<Command.Group class="max-h-60 overflow-y-auto">
								{#each wbsOptions as option (option.value)}
									{@const balance = getAvailableBalance(option.value)}
									<Command.Item
										class="w-full items-center gap-2"
										value={option.label}
										disabled={balance <= 0}
										onSelect={() => {
											transferForm.from_wbs_library_item_id = option.value;
											closeAndFocusTrigger(fromTriggerId, (value) => (fromWbsOpen = value));
										}}
									>
										<div class="flex min-w-0 flex-1 items-center justify-between gap-2">
											<span class="truncate">{option.label}</span>
											<Badge variant={balance > 0 ? 'default' : 'secondary'} class="shrink-0">
												{formatCurrency(balance, {
													symbol: currencySymbol,
													symbolPosition,
													fallback: '0',
												})}
											</Badge>
										</div>
										<CheckIcon
											class={cn(
												'ml-2 size-4 shrink-0',
												option.value !== transferForm.from_wbs_library_item_id &&
													'text-transparent',
											)}
										/>
									</Command.Item>
								{/each}
							</Command.Group>
						</Command.Root>
					</Popover.Content>
				</Popover.Root>
				{#if transferForm.from_wbs_library_item_id}
					{@const availableAmount = getAvailableBalance(transferForm.from_wbs_library_item_id)}
					<div class="text-xs text-gray-500">
						Available: {formatCurrency(availableAmount, {
							symbol: currencySymbol,
							symbolPosition,
							fallback: '0',
						})}
					</div>
				{/if}
			</div>

			<!-- To WBS Item -->
			<div class="space-y-2">
				<Label class="text-sm font-medium">To WBS Item</Label>
				<Popover.Root bind:open={toWbsOpen}>
					<Popover.Trigger
						id={toTriggerId}
						role="combobox"
						class={cn(
							buttonVariants({ variant: 'outline' }),
							'w-full justify-between',
							!transferForm.to_wbs_library_item_id && 'text-muted-foreground',
						)}
						aria-expanded={toWbsOpen}
					>
						<span class="truncate">
							{transferForm.to_wbs_library_item_id
								? wbsOptions.find((opt) => opt.value === transferForm.to_wbs_library_item_id)?.label
								: 'Select target WBS item'}
						</span>
						<CaretUpDownIcon class="ml-2 size-4 shrink-0 opacity-50" />
					</Popover.Trigger>
					<Popover.Content class="w-[--radix-popover-trigger-width] p-0" align="start">
						<Command.Root>
							<Command.Input autofocus placeholder="Search WBS items..." class="h-9" />
							<Command.Empty>No matching items found.</Command.Empty>
							<Command.Group class="max-h-60 overflow-y-auto">
								{#each wbsOptions as option (option.value)}
									<Command.Item
										class="w-full items-center gap-2"
										value={option.label}
										disabled={option.value === transferForm.from_wbs_library_item_id}
										onSelect={() => {
											transferForm.to_wbs_library_item_id = option.value;
											closeAndFocusTrigger(toTriggerId, (value) => (toWbsOpen = value));
										}}
									>
										<span class="truncate">{option.label}</span>
										<CheckIcon
											class={cn(
												'ml-auto size-4 shrink-0',
												option.value !== transferForm.to_wbs_library_item_id && 'text-transparent',
											)}
										/>
									</Command.Item>
								{/each}
							</Command.Group>
						</Command.Root>
					</Popover.Content>
				</Popover.Root>
			</div>

			<!-- Transfer Amount -->
			<div class="space-y-2">
				<div class="flex items-center justify-between">
					<Label class="text-sm font-medium">Transfer Amount</Label>
				</div>
				<div class="flex items-center space-x-2">
					<Input
						type="number"
						min="0"
						step="0.01"
						bind:value={transferForm.transfer_amount}
						placeholder="0.00"
						class="flex-1"
					/>
					<span class="text-sm text-gray-500">{currencySymbol}</span>
				</div>
			</div>

			<!-- Reason -->
			<div class="space-y-2">
				<Label class="text-sm font-medium">Reason</Label>
				<Textarea
					bind:value={transferForm.reason}
					placeholder="Explain why this transfer is needed..."
					rows={3}
				/>
			</div>

			<!-- Validation Result -->
			{#if validationResult}
				<Alert variant={validationResult.is_valid ? 'default' : 'destructive'}>
					<div class="flex items-center">
						{#if validationResult.is_valid}
							<CheckCircleIcon class="mr-2 h-4 w-4" />
						{:else}
							<ExclamationTriangleIcon class="mr-2 h-4 w-4" />
						{/if}
						<AlertDescription>
							{#if validationResult.is_valid}
								Transfer is valid and can be created.
							{:else}
								{validationResult.error_message}
							{/if}
						</AlertDescription>
					</div>
				</Alert>
			{/if}
		</div>

		<Dialog.Footer>
			<Button variant="outline" onclick={() => (createDialogOpen = false)}>Cancel</Button>
			<Button onclick={createTransfer} disabled={!validationResult?.is_valid}>
				Create Transfer
			</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
