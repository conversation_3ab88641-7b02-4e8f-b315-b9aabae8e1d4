import { test, expect } from '@playwright/test';
import { TestDataSetup } from '../utils/mcp-helpers';

test.describe('Tender Management E2E', () => {
	let testOrgName: string;
	let testClientName: string;
	let testDataSetup: TestDataSetup;

	test.beforeAll(async ({ browser }) => {
		// Generate unique test identifiers
		const timestamp = Date.now();
		testOrgName = `Test Org ${timestamp}`;
		testClientName = `Test Client ${timestamp}`;

		// Create a fresh test environment
		const page = await browser.newPage();
		testDataSetup = new TestDataSetup(page);

		// Sign in with test user
		await testDataSetup.signIn();

		// Create organization and client
		await testDataSetup.createOrganization(testOrgName);
		await testDataSetup.createClient(testClientName, 'Test client for tender management tests');

		await page.close();
	});

	test.beforeEach(async ({ page }) => {
		// Initialize test data setup helper
		testDataSetup = new TestDataSetup(page);

		// Sign in with test user
		await testDataSetup.signIn();

		// Navigate to the test client page
		await page.goto(
			`/org/${encodeURIComponent(testOrgName)}/clients/${encodeURIComponent(testClientName)}`,
		);

		// Create a new project for this test
		const projectName = `Tender Test Project ${Date.now()}`;
		await testDataSetup.createProject(projectName, {
			description: 'Test project for tender management tests',
		});

		// Should now be on the project overview page
		await expect(page).toHaveURL(/\/org\/.+\/projects\/.+\/overview/);
		// Verify we're on the overview page by checking for the overview-specific content
		await expect(page.locator('h1')).toContainText('Overview');
	});

	test('should navigate to tenders page', async ({ page }) => {
		// Navigate directly to tenders page since the tab might not be visible for new projects
		const currentUrl = page.url();
		const tendersUrl = currentUrl.replace('/overview', '/tenders');
		await page.goto(tendersUrl);

		// Verify we're on the tenders page
		await expect(page).toHaveURL(/.*\/tenders$/);
		await expect(page.locator('h1')).toContainText('Tenders');
	});

	test('should create a new tender', async ({ page }) => {
		// Navigate directly to tenders page
		const currentUrl = page.url();
		const tendersUrl = currentUrl.replace('/overview', '/tenders');
		await page.goto(tendersUrl);

		// Click New Tender button
		await page.click('text=New Tender');

		// Verify we're on the new tender page
		await expect(page).toHaveURL(/.*\/tenders\/new$/);

		// Fill out the tender form
		await page.fill('input[name="tender_name"]', 'Test E2E Tender');
		await page.fill(
			'textarea[name="description"]',
			'This is a test tender created via E2E testing',
		);

		// Create a new vendor since none exist yet
		await page.getByRole('button', { name: 'Add New Vendor' }).click();

		// Wait for the dialog to open and fill out the vendor form
		await expect(page.locator('text=Create a New Vendor')).toBeVisible({ timeout: 10000 });
		await page.fill('input[name="name"]', 'Test Vendor E2E');
		await page.fill('textarea[name="description"]', 'Test vendor for E2E testing');

		// Select vendor type using the Select component
		await page.locator('[role="dialog"]').getByText('Select vendor type').click();
		await page.getByText('Contractor', { exact: true }).click();

		await page.fill('input[name="contact_name"]', 'John Doe');
		await page.fill('input[name="contact_email"]', '<EMAIL>');

		// Submit the vendor form (click the submit button within the dialog)
		await page.locator('[role="dialog"]').locator('button[type="submit"]').click();

		// Wait for the modal to close and vendor to be selected
		await expect(page.locator('[role="dialog"]')).not.toBeVisible({ timeout: 10000 });

		// Fill optional fields
		await page.fill('textarea[name="notes"]', 'E2E test notes');

		// Submit the form
		await page.click('button[type="submit"]');

		// Should redirect to line items page
		await expect(page).toHaveURL(/.*\/tenders\/.*\/line-items$/);
		await expect(page.locator('h1')).toContainText('Line Items');
	});

	test('should add line items to a tender', async ({ page }) => {
		// Navigate directly to tenders page and create a tender first
		const currentUrl = page.url();
		const tendersUrl = currentUrl.replace('/overview', '/tenders');
		await page.goto(tendersUrl);
		await page.click('text=New Tender');

		// Create a basic tender
		await page.fill('input[name="tender_name"]', 'Test Tender for Line Items');
		await page.fill('textarea[name="description"]', 'Test description');

		// Create a new vendor - wait for the button to be available first
		await expect(page.getByRole('button', { name: 'Add New Vendor' })).toBeVisible({
			timeout: 10000,
		});
		await page.getByRole('button', { name: 'Add New Vendor' }).click();
		await expect(page.locator('[role="dialog"]')).toBeVisible({ timeout: 15000 });
		await page.fill('input[name="name"]', 'Test Vendor Line Items');
		await page.fill('textarea[name="description"]', 'Test vendor for line items');

		// Select vendor type using the Select component
		await page.locator('[role="dialog"]').getByText('Select vendor type').click();
		await page.getByText('Contractor', { exact: true }).click();

		await page.fill('input[name="contact_name"]', 'Jane Doe');
		await page.fill('input[name="contact_email"]', '<EMAIL>');
		await page.locator('[role="dialog"]').locator('button[type="submit"]').click();
		await expect(page.locator('[role="dialog"]')).not.toBeVisible({ timeout: 10000 });
		await page.click('button[type="submit"]');

		// Now we should be on the line items page
		await expect(page).toHaveURL(/.*\/line-items$/);

		// Click Add Line Item button
		await page.click('text=Add Line Item');

		// Fill out the line item form
		await page.fill('input[name="line_number"]', '1');
		await page.fill('textarea[name="description"]', 'Test line item description');
		await page.fill('input[name="quantity"]', '10');
		await page.fill('input[name="unit"]', 'm²');
		await page.fill('input[name="unit_rate"]', '100');

		// Submit the line item form using keyboard (Enter key)
		await page.keyboard.press('Enter');

		// Verify the line item appears in the table
		await expect(page.locator('table')).toContainText('Test line item description');
		await expect(page.locator('table')).toContainText('10');
		await expect(page.locator('table')).toContainText('m²');
		await expect(page.locator('table')).toContainText('100');
	});

	test('should calculate subtotals correctly', async ({ page }) => {
		// Navigate to tenders page
		const currentUrl = page.url();
		const tendersUrl = currentUrl.replace('/overview', '/tenders');
		await page.goto(tendersUrl);

		// If there are existing tenders, click on one
		const firstTender = page.locator('table tbody tr').first();
		if (await firstTender.isVisible()) {
			await firstTender.locator('text=View').click();
			await page.click('text=Line Items');
		} else {
			// Create a new tender and add line items
			await page.click('text=New Tender');
			await page.fill('input[name="tender_name"]', 'Calculation Test Tender');
			await page.fill('textarea[name="description"]', 'Test calculation');

			// Create a new vendor
			await page.getByRole('button', { name: 'Add New Vendor' }).click();
			await expect(page.locator('text=Create a New Vendor')).toBeVisible({ timeout: 10000 });
			await page.fill('input[name="name"]', 'Test Vendor Calculation');
			await page.fill('textarea[name="description"]', 'Test vendor for calculation');

			// Select vendor type using the Select component
			await page.locator('[role="dialog"]').getByText('Select vendor type').click();
			await page.getByText('Contractor', { exact: true }).click();

			await page.fill('input[name="contact_name"]', 'Bob Smith');
			await page.fill('input[name="contact_email"]', '<EMAIL>');
			await page.locator('[role="dialog"]').locator('button[type="submit"]').click();
			await expect(page.locator('[role="dialog"]')).not.toBeVisible({ timeout: 10000 });
			await page.click('button[type="submit"]');
		}

		// Add a line item with known values
		await page.click('text=Add Line Item');
		await page.fill('input[name="line_number"]', '1');
		await page.fill('textarea[name="description"]', 'Calculation test item');
		await page.fill('input[name="quantity"]', '5');
		await page.fill('input[name="unit"]', 'units');
		await page.fill('input[name="unit_rate"]', '200');

		// Trigger calculation by clicking outside the field
		await page.click('textarea[name="description"]');

		// Wait for the subtotal to be automatically calculated (5 * 200 = 1000)
		const subtotalField = page.locator('input[name="subtotal"]');
		// Wait a bit for the calculation to happen
		await page.waitForTimeout(1000);
		await expect(subtotalField).toHaveValue('1000', { timeout: 10000 });

		// Submit the form by dispatching a submit event directly to the form
		await page.locator('[role="dialog"] form').evaluate((form) => {
			if (form instanceof HTMLFormElement) {
				form.requestSubmit();
			}
		});

		// Wait for the dialog to close and the line item to appear in the table
		await expect(page.locator('[role="dialog"]')).not.toBeVisible({ timeout: 10000 });

		// Wait for page to reload and show the table with line items
		await page.waitForLoadState('networkidle');
		await expect(page.locator('table')).toBeVisible({ timeout: 10000 });

		// Verify the calculated subtotal appears in the table (formatted with currency)
		await expect(page.locator('table')).toContainText('1,000 kr');
	});

	test('should handle normalization inputs', async ({ page }) => {
		// Navigate to tenders page
		const currentUrl = page.url();
		const tendersUrl = currentUrl.replace('/overview', '/tenders');
		await page.goto(tendersUrl);

		// Create a new tender for normalization testing
		await page.click('text=New Tender');
		await page.fill('input[name="tender_name"]', 'Normalization Test Tender');
		await page.fill('textarea[name="description"]', 'Test normalization');

		// Create a new vendor - wait for the button to be available first
		await expect(page.getByRole('button', { name: 'Add New Vendor' })).toBeVisible({
			timeout: 10000,
		});
		await page.getByRole('button', { name: 'Add New Vendor' }).click();
		await expect(page.locator('[role="dialog"]')).toBeVisible({ timeout: 15000 });
		await page.fill('input[name="name"]', 'Test Vendor Normalization');
		await page.fill('textarea[name="description"]', 'Test vendor for normalization');

		// Select vendor type using the Select component
		await page.locator('[role="dialog"]').getByText('Select vendor type').click();
		await page.getByText('Contractor', { exact: true }).click();

		await page.fill('input[name="contact_name"]', 'Alice Smith');
		await page.fill('input[name="contact_email"]', '<EMAIL>');
		await page.locator('[role="dialog"]').locator('button[type="submit"]').click();
		await expect(page.locator('[role="dialog"]')).not.toBeVisible({ timeout: 10000 });
		await page.click('button[type="submit"]');

		// Now we should be on the line items page
		await expect(page).toHaveURL(/.*\/line-items$/);

		// Add a line item with normalization
		await page.click('text=Add Line Item');
		await page.fill('input[name="line_number"]', '1');
		await page.fill('textarea[name="description"]', 'Normalization test item');
		await page.fill('input[name="quantity"]', '10');
		await page.fill('input[name="unit"]', 'm²');
		await page.fill('input[name="unit_rate"]', '100');

		// Test percentage normalization (use Shadcn Select component)
		await page.getByText('Fixed Amount').click();
		// Click the specific option in the dropdown, not the calculated display
		await page.getByRole('option', { name: 'Percentage' }).click();

		// Fill the percentage input
		await page.fill('input[type="number"][placeholder="0.00"]', '10');

		// Submit the form using keyboard (Enter key) which should work regardless of viewport
		await page.keyboard.press('Enter');

		// Wait for the dialog to close and the line item to appear in the table
		await expect(page.locator('[role="dialog"]')).not.toBeVisible({ timeout: 10000 });
		await expect(page.locator('table')).toBeVisible({ timeout: 10000 });

		// The normalized amount should be calculated (1000 * 10% = 100)
		await expect(page.locator('table')).toContainText('100'); // normalization amount
	});

	test('should edit existing line items', async ({ page }) => {
		// Navigate to tenders page
		const currentUrl = page.url();
		const tendersUrl = currentUrl.replace('/overview', '/tenders');
		await page.goto(tendersUrl);

		// Find a tender and navigate to its line items
		const firstTender = page.locator('table tbody tr').first();
		if (await firstTender.isVisible()) {
			await firstTender.locator('text=View').click();
			await page.click('text=Line Items');

			// Click edit on the first line item
			const editButton = page
				.locator('table tbody tr')
				.first()
				.locator('button[aria-label="Edit"]');
			if (await editButton.isVisible()) {
				await editButton.click();

				// Modify the description
				await page.fill('textarea[name="description"]', 'Updated line item description');

				// Submit the changes
				await page.click('button[type="submit"]');

				// Verify the changes appear
				await expect(page.locator('table')).toContainText('Updated line item description');
			}
		}
	});

	test('should delete line items with confirmation', async ({ page }) => {
		// Navigate to tenders page
		const currentUrl = page.url();
		const tendersUrl = currentUrl.replace('/overview', '/tenders');
		await page.goto(tendersUrl);

		const firstTender = page.locator('table tbody tr').first();
		if (await firstTender.isVisible()) {
			await firstTender.locator('text=View').click();
			await page.click('text=Line Items');

			// Count initial line items
			const initialCount = await page.locator('table tbody tr').count();

			// Click delete on the first line item
			const deleteButton = page
				.locator('table tbody tr')
				.first()
				.locator('button[aria-label="Delete"]');
			if (await deleteButton.isVisible()) {
				await deleteButton.click();

				// Confirm deletion in the dialog
				await page.click('text=Confirm');

				// Verify the line item was removed
				const newCount = await page.locator('table tbody tr').count();
				expect(newCount).toBe(initialCount - 1);
			}
		}
	});

	test('should update tender status', async ({ page }) => {
		// Navigate to tenders page
		const currentUrl = page.url();
		const tendersUrl = currentUrl.replace('/overview', '/tenders');
		await page.goto(tendersUrl);

		// Find a tender in draft status
		const draftTender = page.locator('table tbody tr').filter({ hasText: 'Draft' }).first();
		if (await draftTender.isVisible()) {
			await draftTender.locator('text=Edit').click();

			// Change status to submitted
			await page.selectOption('select[name="status"]', 'submitted');

			// Submit the form
			await page.click('button[type="submit"]');

			// Verify the status changed
			await expect(page.locator('table')).toContainText('Submitted');
		}
	});

	test('should display tender summary correctly', async ({ page }) => {
		// Navigate to tenders page
		const currentUrl = page.url();
		const tendersUrl = currentUrl.replace('/overview', '/tenders');
		await page.goto(tendersUrl);

		const firstTender = page.locator('table tbody tr').first();
		if (await firstTender.isVisible()) {
			await firstTender.locator('text=View').click();

			// Verify tender details are displayed
			await expect(page.locator('[data-testid="tender-summary"]')).toBeVisible();
			await expect(page.locator('text=Vendor')).toBeVisible();
			await expect(page.locator('text=Status')).toBeVisible();
			await expect(page.locator('text=Total Amount')).toBeVisible();
			await expect(page.locator('text=Line Items')).toBeVisible();
		}
	});
});
